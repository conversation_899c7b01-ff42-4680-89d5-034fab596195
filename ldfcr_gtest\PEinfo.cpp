#pragma once
#include "PEinfo.h"
#include "ldfcr.h"

PEinfo::PEinfo()
{
	m_pe = nullptr;
	PEinitLib(&m_pe);
	m_p.clear();
	m_dllVec.clear();
}


PEinfo::~PEinfo()
{
	PEfreeLib(m_pe);
}

/**
 * 获取预定义的组件顺序列表
 *
 * 此函数返回当前部署环境中实际存在的核心组件的优先级顺序。
 * 根据实际环境调整，只包含当前部署中确实需要检测的组件，
 * 避免输出大量不存在的组件提示信息。
 *
 * 包含的组件类型：
 * - 核心引擎组件: ldfcr, DlpPolicyEngine等
 * - 规则引擎组件: KWRuleEngine, RegexRuleEngine, SVMRuleEngine等
 * - 文档过滤器组件: TrCadFilter, TrPdfFilter, TrOLEFilter等（按需加载）
 * - OCR相关组件: DlpOCR, DlpSCR, TrOCRFilter等（按需加载）
 * - 基础库组件: trcrt, lua, memstream等
 *
 * 注意：某些组件（如Tr系列过滤器）是按需动态加载的，
 * 在程序启动时可能显示"no version number has been established"，
 * 这是正常现象，表示该组件尚未被加载到当前目录。
 *
 * @return vector<string> 包含24个组件名称的有序列表
 */
const vector<string> PEinfo::getOrderedLibs() {
	// 完整的组件列表，包含所有可能的组件（与libaryVersionInfo保持一致）
	static const vector<string> orderedLibs = {
		"ldfcr", "DlpPolicyEngine", "KWRuleEngine", "RegexRuleEngine",
		"FilePropEngine", "FileFpRuleEngine", "SVMRuleEngine", "FpDbRuleEngine",
		"TrCadFilter", "TrCompressFilter", "TrOLEFilter", "TrOOXMLFilter",
		"TrPdfFilter", "TrRtfFilter", "TrTextExtractor", "TrTxtFilter",
		"TrArchive", "TrODFFilter", "DlpOCR", "DlpSCR", "TrOCRFilter",
		"DlpULog", "trcrt", "svm", "memstream"
	};
	return orderedLibs;
}



void PEinfo::GetPEinfo()
{
	vector<wstring> wstrfileVec;
	vector<string> strfileVec;
	/*GetLdfcrRunDll();*/

	wstring wstrCurPath = GetCurModuleFile();
	getCurFiles(wstrCurPath, wstrfileVec);
	for (size_t i = 0; i < wstrfileVec.size(); i++)
	{
		//if (!judgeFileIsWorkPath(wstrfileVec[i].c_str()))
		//{
		//	continue;
		//}
		InsertPEInfo(wstrfileVec[i].c_str(), strfileVec);
	}

	//pdf输出结果
	printf("\n");
	printfVec(strfileVec);
}

wstring PEinfo::GetCurModuleFile()
{
	wchar_t cstrCurPath[MAX_PATH];
	GetModuleFileName(NULL, cstrCurPath, MAX_PATH);
	wstring wstrCurPath = cstrCurPath;
	int cstrpos = wstrCurPath.find_last_of(L"\\");
	wstrCurPath = wstrCurPath.substr(0, cstrpos);
	return wstrCurPath;
}

void PEinfo::getCurFiles(wstring & v_wstrPath, vector<wstring>& v_vecFiles)
{
	// 文件句柄
	long long hFile = 0;
	// 文件信息
	struct _wfinddata_t fileinfo;

	if ((hFile = _wfindfirst(m_p.assign(v_wstrPath).append(L"\\*").c_str(), &fileinfo)) != -1) {
		do {
			if ((wcscmp(fileinfo.name, L".") == 0) || (wcscmp(fileinfo.name, L"..") == 0))  //过滤掉本代表本目录的.和上一级目录的.
			{
				continue;
			}
			// 保存文件的全路径
			if (JudgeFileLogic(fileinfo.name))
			{
				v_vecFiles.push_back(m_p.assign(v_wstrPath).append(L"\\").append(fileinfo.name));
			}

		} while (_wfindnext(hFile, &fileinfo) == 0);  //寻找下一个，成功返回0，否则-1

		_findclose(hFile);
	}
}

/**
 * 不使用配置文件过滤的文件遍历方法
 *
 * 与getCurFiles()的区别：不调用JudgeFileLogic()进行配置文件过滤，
 * 直接收集所有.dll和.exe文件，让后续的isTargetDLL()方法进行智能过滤。
 */
void PEinfo::getCurFilesWithoutFilter(wstring & v_wstrPath, vector<wstring>& v_vecFiles)
{
	// 文件句柄
	long long hFile = 0;
	// 文件信息
	struct _wfinddata_t fileinfo;

	if ((hFile = _wfindfirst(m_p.assign(v_wstrPath).append(L"\\*").c_str(), &fileinfo)) != -1) {
		do {
			if ((wcscmp(fileinfo.name, L".") == 0) || (wcscmp(fileinfo.name, L"..") == 0))  //过滤掉本代表本目录的.和上一级目录的.
			{
				continue;
			}

			// 检查是否为DLL或EXE文件（不使用配置文件过滤）
			wstring fileName = fileinfo.name;
			if (fileName.length() > 4) {
				wstring ext = fileName.substr(fileName.length() - 4);
				std::transform(ext.begin(), ext.end(), ext.begin(), ::towlower);
				if (ext == L".dll" || ext == L".exe") {
					v_vecFiles.push_back(m_p.assign(v_wstrPath).append(L"\\").append(fileinfo.name));
				}
			}

		} while (_wfindnext(hFile, &fileinfo) == 0);  //寻找下一个，成功返回0，否则-1

		_findclose(hFile);
	}
}

/**
 * 检查是否为目标DLL（基于文件名模式匹配，不依赖配置文件）
 *
 * 此方法根据预定义的组件列表，智能判断文件是否为我们关心的组件。
 * 相比JudgeFileLogic()的优势：
 * 1. 不依赖配置文件，避免配置遗漏
 * 2. 支持模糊匹配，能够识别版本号变化的文件
 * 3. 与Linux/Mac版本的逻辑保持一致
 */
bool PEinfo::isTargetDLL(const wstring& filePath)
{
	// 提取文件名（不含路径）
	size_t lastSlash = filePath.find_last_of(L'\\');
	wstring fileName = (lastSlash != wstring::npos) ? filePath.substr(lastSlash + 1) : filePath;

	// 转换为小写以便比较
	wstring lowerFileName = fileName;
	std::transform(lowerFileName.begin(), lowerFileName.end(), lowerFileName.begin(), ::towlower);

	// 移除扩展名
	size_t dotPos = lowerFileName.find_last_of(L'.');
	if (dotPos != wstring::npos) {
		lowerFileName = lowerFileName.substr(0, dotPos);
	}

	// 获取预定义的组件列表
	const vector<string>& orderedLibs = getOrderedLibs();

	// 检查是否匹配任何目标组件
	for (const auto& libName : orderedLibs) {
		wstring wLibName = StringToWstring(libName);
		std::transform(wLibName.begin(), wLibName.end(), wLibName.begin(), ::towlower);

		// 精确匹配
		if (lowerFileName == wLibName) {
			return true;
		}

		// Windows跨平台命名兼容性检查
		// 处理Linux/Mac下为libXXX.so，Windows下为libXXX.dll的情况
		wstring wExpectedLibName = L"lib" + wLibName;  // 例如：svm -> libsvm
		if (lowerFileName == wExpectedLibName) {
			return true;
		}

		// 包含匹配（用于检测改名的组件）
		if (lowerFileName.find(wLibName) != wstring::npos) {
			return true;
		}
	}

	return false;
}

bool PEinfo::JudgeFileLogic(const wchar_t * v_wstrFilePath)
{
	wstring wstrFileName = v_wstrFilePath;
	string strSrcIni = ".\\PETargetFile.ini";
	char LP[1024];
	for (int i = 0; i < 100; i++)
	{
		string name = "ProjectName";
		name.append(to_string(i));
		GetPrivateProfileStringA("project", name.c_str(), "NULL", LP, 512, strSrcIni.c_str());
		string strCm = WstringToString(wstrFileName);
		if (strcmp(LP, strCm.c_str()) == 0)
		{
			return true;
		}
	}
	return false;
}

void PEinfo::InsertPEInfo(const wchar_t * v_wstrFilePath, vector<string>& v_vecFiles)
{
	char chFileName[23] = { 0 };
	char chFileVersion[15] = { 0 };
	char chFileUtime[20] = { 0 };
	char chFileGuid[41] = { 0 };

	if (!m_pe->CheckFile(v_wstrFilePath))
	{
		wcout << v_wstrFilePath << " check error" << endl;
	}
	char * chFilePE = new char[128];

	m_pe->getFilePath(chFilePE);
	string fileName = srcFileName(chFilePE);
	snprintf(chFileName, sizeof(chFileName), "%-23s", fileName.c_str());
	string strFileName = chFileName;

	m_pe->getFileSoftVersion(chFilePE);
	snprintf(chFileVersion, sizeof(chFileVersion), "%-15s", chFilePE);
	string strGoalVersion = chFileVersion;

	m_pe->getFileCreateTime(chFilePE);
	snprintf(chFileUtime, sizeof(chFileUtime), "%-15s", chFilePE);
	string strGoalUtime = chFileUtime;

	m_pe->getFileGuid(chFilePE);
	snprintf(chFileGuid, sizeof(chFileGuid), "%40s", chFilePE);
	string strGuid = chFileGuid;

	string PERes = strFileName + strGoalVersion + strGoalUtime + strGuid;
	v_vecFiles.push_back(PERes);
}

void PEinfo::printfVec(vector<string>& v_vecFiles)
{
	vector<string>::iterator it = v_vecFiles.begin();
	for (; it != v_vecFiles.end(); ++it)
	{
		cout << *it << endl;
	}
}

void PEinfo::GetLdfcrRunDll()
{
	// 直接调用InsertModules，不需要重复初始化ldfcr
	// 因为在main.cpp中已经调用了ldfcr_InitStartup()
	InsertModules();
}

void PEinfo::InsertModules()
{
	DWORD dwpid = GetCurrentProcessId();
	//提升进程权限
	//打开令牌
	HANDLE hToken;//创建令牌句柄
	if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ALL_ACCESS | TOKEN_QUERY, &hToken))//打开令牌句柄，设置令牌权限
	{
		printf("OpenProcessToken Failed.");
		return;
	}
	TOKEN_PRIVILEGES tkp;
	LookupPrivilegeValue(NULL, SE_SHUTDOWN_NAME, &tkp.Privileges[0].Luid);//查看令牌权限
	tkp.PrivilegeCount = 1;
	tkp.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;
	AdjustTokenPrivileges(hToken, FALSE, &tkp, 0, (PTOKEN_PRIVILEGES)NULL, 0);
	if (GetLastError() != ERROR_SUCCESS)
	{
		printf("AdjustTokenPrivileges Failed.");
		return;
	}


	HMODULE hMods[1024];
	HANDLE hProcess;
	DWORD cbNeeded;
	unsigned int i;

	// Print the process identifier.

	/*printf("\nProcess ID: %u\n", dwpid);*/

	// Get a handle to the process.

	hProcess = OpenProcess(PROCESS_QUERY_INFORMATION |
		PROCESS_VM_READ,
		FALSE, dwpid);
	if (NULL == hProcess)
	{
		printf("hProcess is nullptr\n");
		return;
	}

	// Get a list of all the modules in this process.

	if (EnumProcessModules(hProcess, hMods, sizeof(hMods), &cbNeeded))
	{
		for (i = 0; i < (cbNeeded / sizeof(HMODULE)); i++)
		{
			wchar_t szModName[MAX_PATH];

			// Get the full path to the module's file.

			if (GetModuleFileNameEx(hProcess, hMods[i], szModName,
				sizeof(szModName) / sizeof(TCHAR)))
			{
				// Print the module name and handle value.

				m_dllVec.push_back(szModName);
			}
		}
	}

	// Release the handle to the process.

	CloseHandle(hProcess);

	return;
}

bool PEinfo::judgeFileIsWorkPath(const wchar_t * v_wchFilePath)
{
	if (m_dllVec.empty())
	{
		cout << "dll vec get error" << endl;
		return false;
	}
	for (size_t i = 0; i < m_dllVec.size(); ++i)
	{
		if (wcscmp(v_wchFilePath, m_dllVec[i].c_str()) == 0)
		{
			return true;
		}
	}
	wcout << v_wchFilePath << " not found in the running path" << endl;
	return false;
}

std::string PEinfo::WstringToString(const wstring v_wstr)
{
	// wstring转string
	unsigned len = v_wstr.size() * 4;
	setlocale(LC_CTYPE, "");
	char *p = new char[len];
	wcstombs(p, v_wstr.c_str(), len);
	std::string str1(p);
	delete[] p;
	return str1;
}

std::wstring PEinfo::StringToWstring(const string v_str)
{
	// string转wstring的正确实现
	if (v_str.empty()) return L"";

	int len = MultiByteToWideChar(CP_UTF8, 0, v_str.c_str(), -1, NULL, 0);
	if (len == 0) return L"";

	wchar_t* wstr = new wchar_t[len];
	MultiByteToWideChar(CP_UTF8, 0, v_str.c_str(), -1, wstr, len);

	std::wstring result(wstr);
	delete[] wstr;
	return result;
}

std::string PEinfo::srcFileName(const std::string v_wstrPath)
{
	char chPath_buffer[_MAX_PATH];
	char chDrive[_MAX_DRIVE];
	char chDir[_MAX_DIR];
	char chFname[_MAX_FNAME];
	char chExt[_MAX_EXT];
	char chPath[_MAX_EXT];
	//wcscpy(chPath_buffer, __argv[0]);
	_splitpath(v_wstrPath.c_str(), chDrive, chDir, chFname, chExt);
	// Note: _wsplitpath is deprecated; consider using _splitpath_s instead
	std::string strRes = chFname;
	strRes.append(chExt);
	return strRes;
}

/**
 * 收集组件版本信息（仅输出实际加载的组件）
 *
 * 此函数检测程序运行时实际加载的所有模块，然后按预定义顺序输出。
 * 只输出在预定义列表中且实际被加载的组件：
 * 1. 如果在实际加载的模块中找到，输出版本信息
 * 2. 如果找到改名的组件，输出改名错误信息
 * 3. 如果完全没有找到，跳过不输出（不显示"no version number has been established"）
 */
void PEinfo::GetVersionInfoByOrder()
{
	vector<wstring> wstrfileVec;
	vector<string> strfileVec;
	vector<string> strPathVec;

	// 获取实际加载的模块（仅检测真正被依赖的组件）
	GetLdfcrRunDll();  // 填充 m_dllVec
	for (size_t i = 0; i < m_dllVec.size(); i++) {
		wstrfileVec.push_back(m_dllVec[i]);
	}

	// 收集所有实际加载的DLL信息和路径
	for (size_t i = 0; i < wstrfileVec.size(); i++)
	{
		InsertPEInfo(wstrfileVec[i].c_str(), strfileVec);
		string fullPath = WstringToString(wstrfileVec[i]);
		strPathVec.push_back(fullPath);
	}



	// === 按照预定义顺序分析组件状态（仅收集实际加载的组件） ===
	const vector<string>& orderedLibs = getOrderedLibs();

	// 清空之前的收集结果
	m_collectedComponentInfos.clear();
	m_collectedComponentPaths.clear();
	m_collectedComponentNames.clear();

	for (const auto& libName : orderedLibs) {
		bool exactFound = false;
		bool renamedFound = false;
		string renamedInfo = "";
		string renamedPath = "";
		string exactInfo = "";
		string exactPath = "";

		// 在收集的文件中查找匹配的组件
		for (int i = 0; i < strfileVec.size(); ++i) {
			string fileName = srcFileName(strPathVec[i]);
			// 移除.dll和.exe扩展名
			if (fileName.length() > 4) {
				if (fileName.substr(fileName.length() - 4) == ".dll" ||
					fileName.substr(fileName.length() - 4) == ".exe") {
					fileName = fileName.substr(0, fileName.length() - 4);
				}
			}

			// 精确匹配（组件名称完全一致）
			if (fileName == libName) {
				exactFound = true;
				exactInfo = strfileVec[i];
				exactPath = strPathVec[i];
				break;
			}

			// Windows跨平台命名兼容性检查
			// 处理Linux/Mac下为libXXX.so，Windows下为libXXX.dll的情况
			string expectedLibName = "lib" + libName;  // 例如：svm -> libsvm
			if (fileName == expectedLibName) {
				exactFound = true;
				exactInfo = strfileVec[i];
				exactPath = strPathVec[i];
				break;
			}

			// 改名检测（包含原组件名但不完全一致）
			if (fileName.find(libName) != string::npos &&
				fileName != libName &&
				fileName != expectedLibName) {
				renamedFound = true;
				renamedInfo = strfileVec[i];
				renamedPath = strPathVec[i];
				// 继续查找，优先精确匹配
			}
		}

		// 只存储实际找到的组件（精确匹配或改名的）
		if (exactFound) {
			// 找到精确匹配的组件
			m_collectedComponentInfos.push_back(exactInfo);
			m_collectedComponentPaths.push_back(exactPath);
			m_collectedComponentNames.push_back(libName);
		} else if (renamedFound) {
			// 找到改名的组件，生成错误信息
			string errorInfo = libName + " ERROR: Component has been renamed to " + srcFileName(renamedPath);
			m_collectedComponentInfos.push_back(errorInfo);
			m_collectedComponentPaths.push_back(renamedPath);
			m_collectedComponentNames.push_back(libName);
		}
		// 注意：没有找到的组件不再添加到结果中，实现只输出实际依赖的组件
	}
}

/**
 * 输出已收集的版本信息（仅输出实际加载的组件）
 *
 * 输出之前通过GetVersionInfoByOrder()收集的组件信息。
 * 只输出实际找到的组件：
 * 1. 正常组件：显示版本信息和路径
 * 2. 改名组件：显示错误信息和新路径
 * 注意：不再输出未找到的组件
 */
void PEinfo::PrintVersionInfo()
{
	int sequenceNumber = 1;

	for (size_t i = 0; i < m_collectedComponentNames.size(); ++i) {
		string componentInfo = m_collectedComponentInfos[i];

		// 检查是否为改名错误信息
		if (componentInfo.find("ERROR: Component has been renamed") != string::npos) {
			// 输出改名错误信息
			printf("%d. %s\n", sequenceNumber, componentInfo.c_str());
			printf("   Location: %s\n", m_collectedComponentPaths[i].c_str());
		} else {
			// 输出正常的组件信息
			printf("%d. %s\n", sequenceNumber, componentInfo.c_str());
			printf("   Location: %s\n", m_collectedComponentPaths[i].c_str());
		}
		sequenceNumber++;
	}
}



/**
 * 按预定义顺序输出组件信息（已弃用，建议使用GetVersionInfoByOrder + PrintVersionInfo）
 *
 * 此函数用于在程序启动和结束时输出基线库信息，支持动态对比。
 * 现在使用与GetVersionInfoByOrder相同的逻辑，不依赖配置文件过滤。
 *
 * 注意：此方法为了保持向后兼容而保留，建议新代码使用：
 * GetVersionInfoByOrder() + PrintVersionInfo() 的组合。
 */
void PEinfo::GetPEinfoOrdered()
{
	// 直接调用新的收集和输出方法
	GetVersionInfoByOrder();
	PrintVersionInfo();
}

/**
 * 获取程序实际加载的所有模块（真实依赖检测）
 *
 * 此方法不依赖预定义列表，而是获取程序运行时实际加载的所有DLL模块。
 * 这是真正的依赖检测，能够发现：
 * 1. 动态加载的组件
 * 2. 间接依赖的组件
 * 3. 系统库和第三方库
 * 4. 运行时才加载的插件
 */
void PEinfo::GetRealLoadedModules()
{
	printf("=== Getting Real Loaded Modules ===\n");

	// 清空之前的结果
	m_realLoadedModuleInfos.clear();
	m_realLoadedModulePaths.clear();
	m_realLoadedModuleNames.clear();

	// 获取实际加载的模块列表
	GetLdfcrRunDll();  // 填充 m_dllVec

	printf("Found %d loaded modules in process:\n", (int)m_dllVec.size());

	// 处理每个实际加载的模块
	for (size_t i = 0; i < m_dllVec.size(); i++) {
		string modulePath = WstringToString(m_dllVec[i]);
		string moduleName = srcFileName(modulePath);

		// 移除扩展名
		if (moduleName.length() > 4) {
			if (moduleName.substr(moduleName.length() - 4) == ".dll" ||
				moduleName.substr(moduleName.length() - 4) == ".exe") {
				moduleName = moduleName.substr(0, moduleName.length() - 4);
			}
		}

		printf("  Processing: %s\n", modulePath.c_str());

		// 尝试获取模块的版本信息
		vector<string> tempVec;
		InsertPEInfo(m_dllVec[i].c_str(), tempVec);

		if (!tempVec.empty()) {
			// 成功获取版本信息
			m_realLoadedModuleInfos.push_back(tempVec[0]);
			m_realLoadedModulePaths.push_back(modulePath);
			m_realLoadedModuleNames.push_back(moduleName);
		} else {
			// 无法获取版本信息（可能是系统DLL）
			string noVersionInfo = moduleName + " (system module, no version info available)";
			m_realLoadedModuleInfos.push_back(noVersionInfo);
			m_realLoadedModulePaths.push_back(modulePath);
			m_realLoadedModuleNames.push_back(moduleName);
		}
	}

	printf("=== Real Loaded Modules Collection Complete ===\n");
}

/**
 * 输出实际加载的模块信息
 *
 * 按加载顺序输出所有实际加载的模块，包括：
 * 1. 应用程序模块
 * 2. 业务组件模块
 * 3. 系统库模块
 * 4. 第三方库模块
 */
void PEinfo::PrintRealLoadedModules()
{
	printf("\n=== Real Loaded Modules (Actual Dependencies) ===\n");

	if (m_realLoadedModuleInfos.empty()) {
		printf("No loaded modules information available.\n");
		return;
	}

	for (size_t i = 0; i < m_realLoadedModuleInfos.size(); ++i) {
		printf("%d. %s\n", (int)(i + 1), m_realLoadedModuleInfos[i].c_str());
		printf("   Location: %s\n", m_realLoadedModulePaths[i].c_str());
	}

	printf("Total loaded modules: %d\n", (int)m_realLoadedModuleInfos.size());
}



