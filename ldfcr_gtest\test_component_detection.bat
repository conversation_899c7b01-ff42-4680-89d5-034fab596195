@echo off
echo ========================================
echo Windows混合依赖检测功能测试脚本
echo ========================================
echo.

echo 1. 编译项目...
msbuild ldfcr_gtest.sln /p:Configuration=Release /p:Platform=x86 /m

if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo.
echo 2. 运行测试程序（混合检测模式）...
echo.

echo === 运行ldfcr_gtest ===
ldfcr_gtest.exe

echo.
echo === 运行ldfcr_gtest_advance ===
ldfcr_gtest_advance.exe

echo.
echo 3. 测试组件改名检测...
echo.
echo 如果要测试组件改名检测，请手动重命名某个组件文件，例如：
echo - 将 TrCadFilter.dll 重命名为 TrCadFilter_v2.dll
echo - 将 jieba.dll 重命名为 jieba_new.dll
echo 然后重新运行程序，应该会看到改名错误信息。
echo.

echo 4. 测试完成！
echo.
echo 请检查输出中的组件信息：
echo - 混合检测：进程加载的模块 + 文件系统中的组件
echo - libsvm.dll, jieba.dll, lua.dll 等应该能正确显示版本信息
echo - 对于改名的组件，显示"ERROR: Component has been renamed"
echo - 对于不存在的组件，显示"no version number has been established"
echo - 显示组件的完整路径
echo.
echo 特别注意跨平台命名兼容性：
echo - svm 组件会匹配 libsvm.dll（Windows保持lib前缀）
echo - lua 组件会匹配 lua.dll（Windows去掉lib前缀）
echo - jieba 组件会匹配 jieba.dll（Windows去掉lib前缀）
echo.
echo 如果需要查看所有实际加载的模块，请编辑main.cpp取消注释相关代码。
echo.
pause
